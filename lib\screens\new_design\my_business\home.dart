import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/providers/auth_provider.dart';
import 'package:nsl/providers/my_business_home_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/chat_field.dart';
import 'package:nsl/services/multimedia_service.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/widgets/mobile/chat_input_field.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:provider/provider.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class MyBusinessHome extends StatefulWidget {
  const MyBusinessHome({super.key});

  @override
  State<MyBusinessHome> createState() => _MyBusinessHomeState();
}

class _MyBusinessHomeState extends State<MyBusinessHome> {
  final TextEditingController chatController = TextEditingController();
  final FocusNode _chatFocusNode = FocusNode();
  bool showSingleRow = false;

  @override
  void initState() {
    super.initState();
    // Initialize the provider controllers
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<MyBusinessHomeProvider>().initializeControllers();
      _chatFocusNode.addListener(() {
        final wasShowingSingleRow = showSingleRow;
        setState(() {
          showSingleRow = _chatFocusNode.hasFocus;
        });

        // Handle layout mode change when switching modes to avoid sync issues
        if (wasShowingSingleRow != showSingleRow) {
          final provider = context.read<MyBusinessHomeProvider>();
          provider.handleLayoutModeChange(showSingleRow);
        }
      });
    });
  }

  @override
  void dispose() {
    chatController.dispose();
    _chatFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      backgroundColor: Color(0xffF7F9FB),
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        leading: Builder(
          builder: (context) => IconButton(
            icon: Icon(Icons.menu),
            onPressed: () {
              Scaffold.of(context).openDrawer();
            },
          ),
        ),
        title: Text(''),
        backgroundColor: Color(0xffF7F9FB),
        elevation: 0,
        iconTheme: IconThemeData(color: Colors.black),
      ),
      drawer: CustomDrawer(),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
        child: Column(
          children: [
            // Top content that can scroll if needed
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(height: AppSpacing.xs),
                    Center(
                      child: Consumer<AuthProvider>(
                        builder: (context, authProvider, _) {
                          final String firstName =
                              authProvider.user?.username ?? '';
                          final String greeting = AppLocalizations.of(context)
                              .translate('home.greeting')
                              .replaceAll('{name}',
                                  firstName.isNotEmpty ? firstName : '');
                          return Text(
                            greeting,
                            style: FontManager.getCustomStyle(
                                fontSize: FontManager.s24,
                                fontFamily: FontManager.fontFamilyTiemposText),
                          );
                        },
                      ),
                    ),
                    // SizedBox(height: AppSpacing.sm),
                    Consumer<MyBusinessHomeProvider>(
                      builder: (context, provider, child) {
                        return Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Row(
                                  children: [
                                    HoverableSvgIcon(
                                      assetPath:
                                          'assets/images/my_business/home/<USER>',
                                    ),
                                    SizedBox(width: AppSpacing.xxs),
                                    Text(
                                      AppLocalizations.of(context).translate(
                                          'myBusinessHome.favourites'),
                                      style: FontManager.getCustomStyle(
                                          fontFamily:
                                              FontManager.fontFamilyTiemposText,
                                          fontSize: FontManager.s14),
                                    ),
                                  ],
                                ),
                                SearchIconWithPopup(
                                  actionCardsData: provider.actionCardsData,
                                  onToggleFavorite: provider.toggleFavorite,
                                ),
                              ],
                            ),
                            SizedBox(height: AppSpacing.sm),
                            SizedBox(
                              height: showSingleRow
                                  ? MediaQuery.of(context).size.height / 6.5 +
                                      50 // Single row height + indicator
                                  : MediaQuery.of(context).size.height / 2.8,
                              child: _buildActionCardsGrid(provider),
                            ),
                          ],
                        );
                      },
                    ),
                    SizedBox(height: AppSpacing.sm),
                  ],
                ),
              ),
            ),

           

            ChatInputField(
              focusNode: _chatFocusNode,
              chatController: chatController,
              sendMessage: () {},
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionCardsGrid(MyBusinessHomeProvider provider) {
    const double gap = 12;
    final favoriteItems = provider.favoriteItems;

    if (favoriteItems.isEmpty) {
      return Center(
        child: Text(
          'No favorites selected',
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s14,
            color: Colors.grey,
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),
      );
    }

    // Dynamic calculation based on current layout mode
    final int rowsPerPage = showSingleRow ? 1 : 2;
    final int cardsPerPage = rowsPerPage * MyBusinessHomeProvider.cardsPerRow;
    final int pageCount = provider.getDynamicPageCount(showSingleRow);

    final double maxWidth = MediaQuery.of(context).size.width / 1.1;
    final double totalGapsWidth =
        gap * (MyBusinessHomeProvider.cardsPerRow - 1);
    final double cardWidth =
        (maxWidth - totalGapsWidth) / MyBusinessHomeProvider.cardsPerRow;

    return Column(
      children: [
        Expanded(
          child: PageView.builder(
            controller: provider.pageController,
            padEnds: false,
            itemCount: pageCount,
            onPageChanged: (index) {
              // Update provider's current page when user swipes
              provider.updateCurrentPage(index);
            },
            itemBuilder: (context, pageIndex) {
              final int startIndex = pageIndex * cardsPerPage;
              final int endIndex =
                  (startIndex + cardsPerPage).clamp(0, favoriteItems.length);
              final currentItems = favoriteItems.sublist(startIndex, endIndex);

              return Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: List.generate(rowsPerPage, (rowIndex) {
                  final rowStartIndex =
                      rowIndex * MyBusinessHomeProvider.cardsPerRow;

                  // Skip empty rows
                  if (rowStartIndex >= currentItems.length) {
                    return SizedBox.shrink();
                  }

                  return Padding(
                    padding: EdgeInsets.only(
                        bottom: rowIndex < rowsPerPage - 1 ? gap : 0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: List.generate(
                          MyBusinessHomeProvider.cardsPerRow, (i) {
                        final index = rowStartIndex + i;
                        if (index >= currentItems.length) {
                          return SizedBox(
                            width: cardWidth,
                            child: SizedBox.shrink(),
                          );
                        }

                        final item = currentItems[index];
                        final rightGap =
                            i < MyBusinessHomeProvider.cardsPerRow - 1
                                ? gap
                                : 0.0;

                        return Padding(
                          padding: EdgeInsets.only(right: rightGap),
                          child: SizedBox(
                            width: cardWidth,
                            height: MediaQuery.of(context).size.height / 6.5,
                            child: ActionCard(
                              text: item['text'],
                              imagePath: item['image'],
                              iconData: _getIconData(item['icon']),
                              onTap: () {
                                provider.handleActionCardTap(item['action']);
                              },
                            ),
                          ),
                        );
                      }),
                    ),
                  );
                }),
              );
            },
          ),
        ),
        const SizedBox(height: AppSpacing.xs),
        Center(
          child: SmoothPageIndicator(
            controller: provider.pageController!,
            count: pageCount,
            effect: WormEffect(
              dotHeight: 8,
              dotWidth: 8,
              spacing: 10,
              activeDotColor: Color(0xff0058FF),
              dotColor: Colors.grey.shade300,
            ),
          ),
        ),
      ],
    );
  }
}

// Helper function to convert string icon names to IconData
IconData _getIconData(String iconName) {
  switch (iconName) {
    case 'shopping_cart':
      return Icons.shopping_cart;
    case 'people':
      return Icons.people;
    case 'business':
      return Icons.business;
    case 'event_note':
      return Icons.event_note;
    case 'assessment':
      return Icons.assessment;
    case 'school':
      return Icons.school;
    default:
      return Icons.image; // Default icon
  }
}

class SearchIconWithPopup extends StatefulWidget {
  final List<Map<String, dynamic>> actionCardsData;
  final Function(int) onToggleFavorite;

  const SearchIconWithPopup({
    super.key,
    required this.actionCardsData,
    required this.onToggleFavorite,
  });

  @override
  State<SearchIconWithPopup> createState() => _SearchIconWithPopupState();
}

class _SearchIconWithPopupState extends State<SearchIconWithPopup> {
  bool _isPressed = false;
  final TextEditingController _searchController = TextEditingController();

  void _showBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => SearchBottomSheet(
          actionCardsData: widget.actionCardsData,
          onToggleFavorite: (index) {
            widget.onToggleFavorite(index);
            setModalState(() {}); // Update bottom sheet UI
          },
          searchController: _searchController,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => _isPressed = true),
      onTapUp: (_) => setState(() => _isPressed = false),
      onTapCancel: () => setState(() => _isPressed = false),
      onTap: _showBottomSheet,
      behavior:
          HitTestBehavior.translucent, // Ensures transparent tap area works
      child: ConstrainedBox(
        constraints: BoxConstraints.tightFor(
            width: 40, height: 40), // Increased touch area
        child: Align(
          alignment: Alignment.center,
          child: SvgPicture.asset(
            'assets/images/my_business/home/<USER>',
            width: 18,
            height: 18,
            colorFilter: ColorFilter.mode(
              _isPressed ? Color(0xff0058FF) : Colors.black,
              BlendMode.srcIn,
            ),
          ),
        ),
      ),
    );
  }
}

class SearchBottomSheet extends StatefulWidget {
  final List<Map<String, dynamic>> actionCardsData;
  final Function(int) onToggleFavorite;
  final TextEditingController searchController;

  const SearchBottomSheet({
    super.key,
    required this.actionCardsData,
    required this.onToggleFavorite,
    required this.searchController,
  });

  @override
  State<SearchBottomSheet> createState() => _SearchBottomSheetState();
}

class _SearchBottomSheetState extends State<SearchBottomSheet> {
  List<Map<String, dynamic>> _filteredItems = [];
  final FocusNode _focusNode = FocusNode();
  bool _isSearchFocused = false;

  @override
  void initState() {
    super.initState();
    _filteredItems = List.from(widget.actionCardsData);
    widget.searchController.addListener(_filterMenuItems);
    _focusNode.addListener(() {
      setState(() {
        _isSearchFocused = _focusNode.hasFocus;
      });
    });
    // Remove auto-focus - let user tap to focus
  }

  void _filterMenuItems() {
    setState(() {
      final query = widget.searchController.text.toLowerCase();
      _filteredItems = widget.actionCardsData
          .where((item) => item['text'].toLowerCase().contains(query))
          .toList();
    });
  }

  @override
  void dispose() {
    widget.searchController.removeListener(_filterMenuItems);
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.75,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Add Favourites Header Row
          Container(
            padding:
                const EdgeInsets.symmetric(horizontal: 20.0, vertical: 16.0),
            child: Row(
              children: [
                SvgPicture.asset(
                  'assets/images/my_business/home/<USER>',
                  width: 20,
                  height: 20,
                  colorFilter: ColorFilter.mode(Colors.black, BlendMode.srcIn),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Add Favourites',
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s16,
                      fontWeight: FontManager.medium,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  behavior: HitTestBehavior
                      .translucent, // Allows taps in transparent area
                  child: ConstrainedBox(
                    constraints: BoxConstraints.tightFor(
                        width: 40, height: 40), // Enlarged tap area
                    child: Align(
                      alignment: Alignment.center,
                      child: Icon(
                        Icons.close,
                        size: 20,
                        color: Colors.grey[600],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Search Field
          Padding(
            padding: const EdgeInsets.all(15.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: EdgeInsets.only(bottom: 10),
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: Colors.grey.shade300,
                        width: 1,
                      ),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: widget.searchController,
                          focusNode: _focusNode,
                          decoration: InputDecoration(
                            hintText: 'Search',
                            contentPadding: EdgeInsets.zero,
                            border: InputBorder.none,
                            enabledBorder: InputBorder.none,
                            focusedBorder: InputBorder.none,
                            isDense: true,
                            hintStyle: FontManager.getCustomStyle(
                              color: Color(0xffD0D0D0),
                              fontSize: FontManager.s16,
                              fontWeight: FontManager.regular,
                              fontFamily: FontManager.fontFamilyTiemposText,
                            ),
                          ),
                          style: FontManager.getCustomStyle(
                            fontSize: FontManager.s14,
                            fontWeight: FontManager.regular,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          if (_isSearchFocused &&
                              widget.searchController.text.isNotEmpty) {
                            // Clear text when close icon is tapped
                            widget.searchController.clear();
                          } else if (!_isSearchFocused) {
                            // Focus search field when search icon is tapped
                            _focusNode.requestFocus();
                          }
                        },
                        child: Padding(
                          padding: const EdgeInsets.only(right: 10.0),
                          child: _isSearchFocused &&
                                  widget.searchController.text.isNotEmpty
                              ? Icon(
                                  Icons.close,
                                  size: 20,
                                  color: Colors.grey.shade600,
                                )
                              : SvgPicture.asset(
                                  'assets/images/my_business/search_collection.svg',
                                  width: 20,
                                  height: 20,
                                  colorFilter: ColorFilter.mode(
                                      Colors.black, BlendMode.srcIn),
                                ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Menu Items
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.symmetric(horizontal: 20),
              itemCount: _filteredItems.length,
              itemBuilder: (context, index) {
                final item = _filteredItems[index];
                final originalIndex = widget.actionCardsData.indexOf(item);
                return BottomSheetMenuItem(
                  text: item['text'],
                  isSelected: item['isSelected'] ?? false,
                  onToggleFavorite: () =>
                      widget.onToggleFavorite(originalIndex),
                  onTap: () {
                    debugPrint('${item['action']} tapped');
                    Navigator.of(context).pop();
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class BottomSheetMenuItem extends StatefulWidget {
  final String text;
  final VoidCallback onTap;
  final bool isSelected;
  final VoidCallback onToggleFavorite;

  const BottomSheetMenuItem({
    super.key,
    required this.text,
    required this.onTap,
    required this.onToggleFavorite,
    this.isSelected = false,
  });

  @override
  State<BottomSheetMenuItem> createState() => _BottomSheetMenuItemState();
}

class _BottomSheetMenuItemState extends State<BottomSheetMenuItem> {
  bool isStarPressed = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 0, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Color(0xFFF0F0F0), width: 0.5),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: GestureDetector(
              onTap: widget.onTap,
              child: Text(
                widget.text,
                overflow: TextOverflow.ellipsis,
                softWrap: false,
                maxLines: 1,
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s16,
                  fontWeight: FontManager.regular,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black87,
                ),
              ),
            ),
          ),
          GestureDetector(
            onTapDown: (_) => setState(() => isStarPressed = true),
            onTapUp: (_) => setState(() => isStarPressed = false),
            onTapCancel: () => setState(() => isStarPressed = false),
            onTap: () {
              widget.onToggleFavorite();
              debugPrint('Star tapped for ${widget.text}');
            },
            child: Container(
              padding: EdgeInsets.all(6),
              child: SvgPicture.asset(
                'assets/images/my_business/home/<USER>',
                width: 20,
                height: 20,
                colorFilter: ColorFilter.mode(
                  widget.isSelected
                      ? Color(0xff0058FF)
                      : (isStarPressed ? Color(0xff0058FF) : Color(0xffD0D0D0)),
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class HoverableSvgIcon extends StatefulWidget {
  final String assetPath;

  const HoverableSvgIcon({super.key, required this.assetPath});

  @override
  State<HoverableSvgIcon> createState() => _HoverableSvgIconState();
}

class _HoverableSvgIconState extends State<HoverableSvgIcon> {
  bool isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => isPressed = true),
      onTapUp: (_) => setState(() => isPressed = false),
      onTapCancel: () => setState(() => isPressed = false),
      onTap: () {
        debugPrint('Icon tapped: ${widget.assetPath}');
      },
      child: SvgPicture.asset(
        widget.assetPath,
        width: 16,
        height: 16,
        colorFilter: ColorFilter.mode(
          isPressed ? Color(0xff0055FF) : Colors.black,
          BlendMode.srcIn,
        ),
      ),
    );
  }
}

class ActionCard extends StatefulWidget {
  final String text;
  final String? imagePath;
  final IconData? iconData;
  final VoidCallback? onTap;
  final double? elevation;
  final EdgeInsetsGeometry? padding;
  final TextStyle? textStyle;
  final Color? iconColor;
  final double? iconSize;

  const ActionCard({
    super.key,
    required this.text,
    this.imagePath,
    this.iconData,
    this.onTap,
    this.elevation = 1,
    this.padding = const EdgeInsets.all(10.0),
    this.textStyle,
    this.iconColor = Colors.grey,
    this.iconSize = 24,
  });

  @override
  State<ActionCard> createState() => _ActionCardState();
}

class _ActionCardState extends State<ActionCard> {
  bool isPressed = false;

  @override
  Widget build(BuildContext context) {
    final defaultTextStyle = FontManager.getCustomStyle(
      fontSize: FontManager.s12,
      fontWeight: FontManager.regular,
      fontFamily: FontManager.fontFamilyTiemposText,
      color: Colors.black,
    );

    return GestureDetector(
      onTapDown: (_) => setState(() => isPressed = true),
      onTapUp: (_) => setState(() => isPressed = false),
      onTapCancel: () => setState(() => isPressed = false),
      onTap: widget.onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: isPressed ? const Color(0xff0058FF) : Color(0xffD0D0D0),
            width: isPressed ? 1 : 0.5,
          ),
        ),
        padding: const EdgeInsets.all(10.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image/Icon
            Expanded(
              flex: 3,
              child: SizedBox(
                width: double.infinity,
                child: widget.imagePath != null
                    ? Image.asset(
                        widget.imagePath!,
                        fit: BoxFit.cover,
                      )
                    : Icon(
                        widget.iconData ?? Icons.image,
                        size: widget.iconSize,
                        color: isPressed
                            ? const Color(0xff0058FF)
                            : widget.iconColor,
                      ),
              ),
            ),
            const SizedBox(height: AppSpacing.xs),
            // Text
            Expanded(
              flex: 2,
              child: Text(
                widget.text,
                style: widget.textStyle ?? defaultTextStyle,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
